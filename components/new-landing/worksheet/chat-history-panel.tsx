

'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

interface ChatHistoryPanelProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
}

interface ChatSession {
  prompt: string;
  timestamp: string;
  userId: string;
}

export default function ChatHistoryPanel({ isOpen, onClose, userId }: ChatHistoryPanelProps) {
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const router = useRouter();

  useEffect(() => {
    if (!userId) return;

    const fetchHistory = async () => {
      try {
        const res = await fetch(`/api/chat-history?userId=${userId}`);
        if (!res.ok) throw new Error('Failed to fetch chat history');
        const sessions = await res.json();
        setChatSessions(sessions);
      } catch (err) {
        console.error('Failed to load chat history from API:', err);
      }
    };

    if (isOpen) {
      fetchHistory();
    }
  }, [userId, isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed top-0 left-0 w-full h-full bg-black/40 backdrop-blur-sm z-50">
      <div className="absolute top-20 left-4 max-w-sm w-full bg-white rounded-xl shadow-lg p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Chat History</h3>
          <button onClick={onClose} className="text-sm text-gray-600 hover:text-black">
            Close
          </button>
        </div>
        <div className="space-y-3 max-h-[60vh] overflow-y-auto">
          {chatSessions.length === 0 && (
            <p className="text-sm text-gray-500">No previous chats yet.</p>
          )}
          {chatSessions.map((session, idx) => (
            <button
              key={idx}
              onClick={() => {
                router.push(`/app/worksheet?prompt=${encodeURIComponent(session.prompt)}`);
                onClose();
              }}
              className="w-full text-left border border-gray-200 p-3 rounded-lg hover:bg-gray-50 transition"
            >
              <div className="text-sm font-medium truncate">
                {session.prompt.slice(0, 80) || 'Untitled Prompt'}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {new Date(session.timestamp).toLocaleString()}
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}