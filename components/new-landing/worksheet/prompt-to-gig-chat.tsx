

'use client';

import { useEffect, useState, useRef } from 'react';

interface PromptToGigChatProps {
  prompt: string;
}

interface ChatMessage {
  role: 'user' | 'ai';
  content: string;
}

export default function PromptToGigChat({ prompt }: PromptToGigChatProps) {
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([
    { role: 'user', content: prompt },
  ]);
  const [loading, setLoading] = useState(false);
  const bottomRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const runInitialQuery = async () => {
      setLoading(true);
      try {
        const res = await fetch('/api/ai-intake/freelancer', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ intent: prompt, step: 'initial' }),
        });
        const data = await res.json();
        const reply = data.result || data.error || 'No response received.';
        setChatHistory((prev) => [...prev, { role: 'ai', content: reply }]);
      } catch (err) {
        setChatHistory((prev) => [...prev, { role: 'ai', content: 'Something went wrong.' }]);
      } finally {
        setLoading(false);
      }
    };

    runInitialQuery();
  }, [prompt]);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatHistory, loading]);

  // Persist chat session after initial AI reply
  useEffect(() => {
    if (chatHistory.length < 2) return; // Only send once both user + ai message exist

    const saveSession = async () => {
      const userId = (await import('next-auth/react')).useSession().data?.user?.id;
      if (!userId) return;

      try {
        await fetch('/api/chat-history', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId,
            prompt,
            messages: chatHistory
          })
        });
      } catch (err) {
        console.error('Failed to save chat session:', err);
      }
    };

    saveSession();
  }, [chatHistory]);

  return (
    <div className="h-screen w-full bg-white flex flex-col overflow-hidden">
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        {chatHistory.map((msg, idx) => (
          <div
            key={idx}
            className={`max-w-xl rounded-lg px-4 py-2 text-sm whitespace-pre-wrap ${
              msg.role === 'user'
                ? 'ml-auto bg-black text-white'
                : 'mr-auto bg-gray-100 text-gray-900'
            }`}
          >
            {msg.content}
          </div>
        ))}
        {loading && (
          <div className="mr-auto bg-gray-100 text-gray-900 rounded-lg px-4 py-2 text-sm animate-pulse">
            Generating response...
          </div>
        )}
        <div ref={bottomRef} />
      </div>

      <div className="border-t p-4 bg-white shadow-inner">
        <div className="flex justify-end gap-3">
          <button
            onClick={() => {
              window.location.href = '/freelancer-dashboard/proposals/create?fromPrompt=' + encodeURIComponent(prompt);
            }}
            className="bg-black text-white px-4 py-2 rounded-lg text-sm hover:bg-gray-800 transition-colors"
          >
            Create Proposal
          </button>
          <button
            onClick={() => {
              window.location.href = '/freelancer-dashboard/gigs?prompt=' + encodeURIComponent(prompt);
            }}
            className="bg-gray-200 text-gray-900 px-4 py-2 rounded-lg text-sm hover:bg-gray-300 transition-colors"
          >
            Find Matching Gigs
          </button>
        </div>
      </div>
    </div>
  );
}