

'use client';

import { useEffect, useRef, useState } from 'react';

interface PromptToProjectChatProps {
  prompt: string;
}

interface ChatMessage {
  role: 'user' | 'ai';
  content: string;
}

export default function PromptToProjectChat({ prompt }: PromptToProjectChatProps) {
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([
    { role: 'user', content: prompt },
  ]);
  const [step, setStep] = useState<'initial' | 'budget' | 'confirmation' | 'done'>('initial');
  const [budget, setBudget] = useState('');
  const [loading, setLoading] = useState(false);
  const bottomRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const fetchInitialResponse = async () => {
      setLoading(true);
      try {
        const res = await fetch('/api/ai-intake/client', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ prompt, step }),
        });
        const data = await res.json();
        const reply = data.result || data.error || 'No response received.';
        setChatHistory((prev) => [...prev, { role: 'ai', content: reply }]);

        if (data.step === 'budget_request') {
          setStep('budget');
        } else if (data.step === 'requirements_confirmation') {
          setStep('confirmation');
        }
      } catch (err) {
        setChatHistory((prev) => [...prev, { role: 'ai', content: 'Something went wrong.' }]);
      } finally {
        setLoading(false);
      }
    };

    fetchInitialResponse();
  }, [prompt]);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatHistory, loading]);

  const handleBudgetSubmit = async () => {
    if (!budget.trim()) return;

    setChatHistory((prev) => [...prev, { role: 'user', content: `$${budget} budget.` }]);
    setLoading(true);

    try {
      const res = await fetch('/api/ai-intake/client', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt, budget, step: 'budget' }),
      });
      const data = await res.json();
      const reply = data.result || data.error || 'No response received.';
      setChatHistory((prev) => [...prev, { role: 'ai', content: reply }]);

      if (data.step === 'requirements_confirmation') {
        setStep('confirmation');
      }
    } catch (err) {
      setChatHistory((prev) => [...prev, { role: 'ai', content: 'Something went wrong.' }]);
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = () => {
    window.location.href = '/commissioner-dashboard/projects/create?fromPrompt=' + encodeURIComponent(prompt);
  };

  return (
    <div className="h-screen w-full bg-black/10 backdrop-blur-sm flex flex-col overflow-hidden">
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        {chatHistory.map((msg, idx) => (
          <div
            key={idx}
            className={`max-w-xl rounded-xl px-4 py-3 text-sm whitespace-pre-wrap shadow-md transition-all ${
              msg.role === 'user'
                ? 'ml-auto bg-gray-900 text-white'
                : 'mr-auto bg-white/90 backdrop-blur-sm text-gray-900 border border-gray-300'
            }`}
            style={{ fontFamily: 'Plus Jakarta Sans' }}
          >
            {msg.content}
          </div>
        ))}

        {loading && (
          <div className="mr-auto bg-white/90 backdrop-blur-sm text-gray-900 rounded-xl px-4 py-3 text-sm animate-pulse shadow-md border border-gray-300">
            Generating response...
          </div>
        )}

        <div ref={bottomRef} />
      </div>

      {/* Input for Budget Step */}
      {step === 'budget' && !loading && (
        <div className="border-t border-gray-200 p-6 bg-white/90 backdrop-blur-sm shadow-lg">
          <div className="flex gap-3 items-center">
            <div className="flex items-center bg-gray-50 border border-gray-300 rounded-xl px-4 py-3 text-sm flex-1">
              <span className="text-gray-600 font-medium mr-2">$</span>
              <input
                type="number"
                placeholder="Your budget"
                className="bg-transparent border-none outline-none flex-1 text-sm"
                style={{ fontFamily: 'Plus Jakarta Sans' }}
                value={budget}
                onChange={(e) => setBudget(e.target.value)}
              />
              <span className="text-gray-500 text-xs ml-1">USD</span>
            </div>
            <button
              onClick={handleBudgetSubmit}
              className="bg-gray-900 text-white px-6 py-3 rounded-xl text-sm font-medium hover:bg-gray-800 transition-colors shadow-md"
              style={{ fontFamily: 'Plus Jakarta Sans' }}
            >
              Send
            </button>
          </div>
        </div>
      )}

      {/* Confirmation Step */}
      {step === 'confirmation' && !loading && (
        <div className="border-t border-gray-200 p-6 bg-white/90 backdrop-blur-sm shadow-lg">
          <button
            onClick={handleConfirm}
            className="w-full bg-gray-900 text-white py-4 rounded-xl text-sm font-medium hover:bg-gray-800 transition-colors shadow-md"
            style={{ fontFamily: 'Plus Jakarta Sans' }}
          >
            Confirm & Continue
          </button>
        </div>
      )}
    </div>
  );
}