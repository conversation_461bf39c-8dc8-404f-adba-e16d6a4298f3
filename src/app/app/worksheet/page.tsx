

// src/app/app/worksheet/page.tsx
'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Navbar1 from '../../../../components/navbar1';
import FreelancerTopNavbar from '../../../../components/freelancer-dashboard/top-navbar';
import CommissionerTopNavbar from '../../../../components/commissioner-dashboard/top-navbar';
import PromptToGigChat from '../../../../components/new-landing/worksheet/prompt-to-gig-chat';
import PromptToProjectChat from '../../../../components/new-landing/worksheet/prompt-to-project-chat';

export default function WorksheetPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const prompt = decodeURIComponent(searchParams.get('prompt') || '');
  const [mode, setMode] = useState<'building' | 'executing'>('building');

  useEffect(() => {
    if (status === 'loading') return;
    if (!session?.user) {
      // Not logged in, redirect to login with redirect and prompt
      router.push(`/auth/login?redirect=/app/worksheet&prompt=${encodeURIComponent(prompt)}`);
    } else {
      const userType = (session.user as any)?.userType;
      setMode(userType === 'freelancer' ? 'executing' : 'building');
    }
  }, [session, status, router, prompt]);

  const renderNavbar = () => {
    if (!session) return <Navbar1 />;
    const userType = (session.user as any)?.userType;
    if (userType === 'freelancer') return <FreelancerTopNavbar />;
    if (userType === 'commissioner') return <CommissionerTopNavbar />;
    return <Navbar1 />;
  };

  const backgroundImagePath = `/images/pages/${mode === 'building' ? 'commisioners' : 'freelancers'}.png`;

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background only visible behind worksheet */}
      <img
        src={backgroundImagePath}
        alt="Background"
        className="fixed inset-0 w-full h-full object-cover z-0"
      />

      {/* Content Layer */}
      <div className="relative z-10 flex flex-col h-screen">
        <header className="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
          {renderNavbar()}
        </header>

        <main className="flex-1 overflow-hidden">
          {/* Chat panel with grayscale theme - background shows through */}
          <div className="h-full relative">
            {session?.user && (session.user as any)?.userType === 'freelancer' && (
              <PromptToGigChat prompt={prompt} />
            )}
            {session?.user && (session.user as any)?.userType === 'commissioner' && (
              <PromptToProjectChat prompt={prompt} />
            )}
          </div>
        </main>
      </div>
    </div>
  );
}